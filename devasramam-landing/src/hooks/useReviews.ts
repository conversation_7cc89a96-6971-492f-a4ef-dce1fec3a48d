import { useState, useEffect } from 'react';

export interface Review {
  text: string;
  author: string;
  date: string;
  rating: number;
}

interface ReviewsResponse {
  reviews: Review[];
  total: number;
  success: boolean;
}

export function useReviews() {
  const [reviews, setReviews] = useState<Review[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchReviews = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/reviews');
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data: ReviewsResponse = await response.json();
        
        if (data.success) {
          setReviews(data.reviews);
        } else {
          throw new Error('Failed to fetch reviews');
        }
      } catch (err) {
        console.error('Error fetching reviews:', err);
        setError(err instanceof Error ? err.message : 'Failed to load reviews');
        // Fallback to empty array on error
        setReviews([]);
      } finally {
        setLoading(false);
      }
    };

    // Use requestIdleCallback if available, otherwise setTimeout
    if (typeof window !== 'undefined') {
      if ('requestIdleCallback' in window) {
        window.requestIdleCallback(() => {
          fetchReviews();
        });
      } else {
        setTimeout(fetchReviews, 100);
      }
    } else {
      fetchReviews();
    }
  }, []);

  return { reviews, loading, error };
}
