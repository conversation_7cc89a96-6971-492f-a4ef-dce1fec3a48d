"use client";

import { useState } from "react";
import { Star } from "lucide-react";
import { Card } from "@/components/ui/card";
import { Marquee } from "@/components/ui/marquee";
import { motion } from "framer-motion";
import Image from "next/image";
import { BRAND, TESTIMONIALS } from "@/lib/constants";
import { useReviews } from "@/hooks/useReviews";
import ReviewModal from "@/components/ui/ReviewModal";

const firstRow = testimonials.slice(0, testimonials.length / 2);
const secondRow = testimonials.slice(testimonials.length / 2);

// Function to truncate text to first two sentences
function truncateToTwoSentences(text: string): string {
  const sentences = text.match(/[^\.!?]+[\.!?]+/g);
  if (!sentences || sentences.length <= 2) return text;
  return sentences.slice(0, 2).join('').trim();
}

function TestimonialCard({ name, location, rating, text, avatar, originalIndex, onCardClick }: typeof testimonials[0] & { onCardClick: (index: number) => void }) {
  const truncatedText = truncateToTwoSentences(text);

  return (
    <Card
      className="w-72 p-5 mx-3 bg-white/80 backdrop-blur-sm border-none shadow-lg hover:shadow-xl transition-all duration-300 group cursor-pointer hover:scale-105"
      onClick={() => onCardClick(originalIndex)}
    >
      <div className="flex items-start space-x-3">
        <div className="w-10 h-10 rounded-full bg-[#004D40] text-white flex items-center justify-center font-semibold text-xs">
          {avatar}
        </div>
        <div className="flex-1">
          <div className="flex items-center space-x-2 mb-2">
            <h4 className="font-semibold text-[#333333] text-sm">{name}</h4>
            <span className="text-xs text-[#666666]">• {location}</span>
          </div>
          <div className="flex items-center space-x-1 mb-2">
            {[...Array(rating)].map((_, i) => (
              <Star key={i} size={12} className="fill-yellow-400 text-yellow-400" />
            ))}
          </div>
          <p className="text-xs text-[#666666] leading-relaxed group-hover:text-[#333333] transition-colors">
            "{truncatedText}"
          </p>
        </div>
      </div>
    </Card>
  );
}

export default function TestimonialsMarquee() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedReviewIndex, setSelectedReviewIndex] = useState(0);
  const { reviews, loading, error } = useReviews();

  const handleCardClick = (index: number) => {
    setSelectedReviewIndex(index);
    setIsModalOpen(true);
  };

  // Convert reviews to the format expected by this component
  const testimonials = reviews.map((testimonial, index) => ({
    name: testimonial.author,
    location: "Verified Guest",
    rating: testimonial.rating,
    text: testimonial.text,
    avatar: testimonial.author.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2),
    originalIndex: index
  }));

  // Use fallback testimonials from constants if reviews are still loading
  const fallbackTestimonials = TESTIMONIALS.map((testimonial, index) => ({
    name: testimonial.author,
    location: "Verified Guest",
    rating: testimonial.rating,
    text: testimonial.text,
    avatar: testimonial.author.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2),
    originalIndex: index
  }));

  const displayTestimonials = loading || error ? fallbackTestimonials : testimonials;

  // Split testimonials into two rows for the marquee effect
  const firstRow = displayTestimonials.slice(0, Math.ceil(displayTestimonials.length / 2));
  const secondRow = displayTestimonials.slice(Math.ceil(displayTestimonials.length / 2));

  return (
    <>
      <ReviewModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        initialReviewIndex={selectedReviewIndex}
      />
    <section id="reviews" className="relative py-20 bg-gradient-to-br from-[#004D40]/5 via-white to-[#004D40]/5 overflow-hidden">
      {/* Aesthetic Background Shapes */}
      <div className="absolute top-10 right-10 w-32 h-32 bg-[#004D40]/10 rounded-full blur-2xl"></div>
      <div className="absolute bottom-20 left-16 w-24 h-24 bg-[#8BC34A]/15 rounded-full blur-xl"></div>
      <div className="absolute top-1/2 right-1/4 w-16 h-16 bg-[#004D40]/8 transform rotate-45 blur-lg"></div>
      <div className="absolute bottom-10 right-20 w-20 h-20 bg-[#8BC34A]/12 transform rotate-12 blur-md"></div>

      <div className="container mx-auto px-6 relative">
        {/* Header */}
        <motion.div
          className="relative text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          {/* Extra Large Guest Favorite Logo - Shifted Up */}
          <div className="relative flex justify-center mb-2 -mt-4">
            <Image
              src="/assets/images/guest_favorite_icon.png"
              alt="Devasramam Guest Reviews"
              width={220}
              height={220}
              className="object-contain relative z-10"
            />
          </div>

          {/* Text overlapping with logo */}
          <div className="relative -mt-12 pt-12">
            <h2 className="text-4xl md:text-5xl font-bold text-[#333333] mb-4" style={{ fontFamily: 'var(--font-lora)' }}>
              What Our Guests Say
            </h2>
            <p className="text-xl text-[#666666] max-w-2xl mx-auto">
              Discover why travelers from around the world choose Devasramam for their Kerala experience
            </p>
          </div>
        </motion.div>

        {/* Testimonials Marquee */}
        <div className="relative">
          {/* Additional decorative shapes around marquee */}
          <div className="absolute -top-8 left-1/3 w-12 h-12 bg-[#004D40]/6 transform rotate-45 blur-sm"></div>
          <div className="absolute -bottom-6 right-1/3 w-14 h-14 bg-[#8BC34A]/8 rounded-full blur-md"></div>

          <Marquee pauseOnHover className="[--duration:60s]">
            {firstRow.map((testimonial, index) => (
              <TestimonialCard key={`first-${index}`} {...testimonial} onCardClick={handleCardClick} />
            ))}
          </Marquee>
          <Marquee reverse pauseOnHover className="[--duration:60s] mt-6">
            {secondRow.map((testimonial, index) => (
              <TestimonialCard key={`second-${index}`} {...testimonial} onCardClick={handleCardClick} />
            ))}
          </Marquee>

          {/* Gradient overlays */}
          <div className="pointer-events-none absolute inset-y-0 left-0 w-1/3 bg-gradient-to-r from-white via-white/50 to-transparent"></div>
          <div className="pointer-events-none absolute inset-y-0 right-0 w-1/3 bg-gradient-to-l from-white via-white/50 to-transparent"></div>
        </div>

        {/* Stats with decorative elements */}
        <motion.div
          className="relative grid grid-cols-1 md:grid-cols-3 gap-8 mt-16 text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
        >
          {/* Decorative shapes around stats */}
          <div className="absolute -top-4 left-8 w-8 h-8 bg-[#8BC34A]/10 rounded-full blur-sm"></div>
          <div className="absolute -bottom-2 right-12 w-10 h-10 bg-[#004D40]/8 transform rotate-45 blur-md"></div>

          <div className="relative">
            <div className="text-4xl font-bold text-[#004D40] mb-2" style={{ fontFamily: 'var(--font-lora)' }}>{BRAND.stats.totalGuests}+</div>
            <div className="text-[#666666]">Happy Guests</div>
          </div>
          <div className="relative">
            <div className="text-4xl font-bold text-[#004D40] mb-2" style={{ fontFamily: 'var(--font-lora)' }}>{BRAND.stats.rating}</div>
            <div className="text-[#666666]">Average Rating</div>
          </div>
          <div className="relative">
            <div className="text-4xl font-bold text-[#004D40] mb-2" style={{ fontFamily: 'var(--font-lora)' }}>98%</div>
            <div className="text-[#666666]">Would Recommend</div>
          </div>
        </motion.div>
      </div>
    </section>
    </>
  );
}
