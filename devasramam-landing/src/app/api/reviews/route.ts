import { NextResponse } from 'next/server';

// All 4-5 star testimonials for lazy loading
const ALL_REVIEWS = [
  {
    text: "The place was exactly as mentioned.Clean and tidy. Host was extremely friendly and easy to approach.Restaurants are nearby and Swiggy zomato delivery available.All in all excellent stay.Would love to stay there again.Special mention for the small library which has a treasure trove of books.",
    author: "Arun",
    date: "June 2025",
    rating: 5
  },
  {
    text: "The room was so nice and comfortable. They are such a beautiful and warm family. I feel like i was part of their family. They helped me a lot. I made so many special memories with them. they made my heart warm all the time. I was always feeling safe because of them i will definitely come back to my second home 😊 thank you",
    author: "<PERSON>",
    date: "February 2025",
    rating: 5
  },
  {
    text: "<PERSON><PERSON><PERSON> was a great host. Very kind and her place was really great to stay.Especially since I had to check-in early in the morning <PERSON><PERSON><PERSON> was on call for any needs. Definitely recommend 💯. Thanks for the stay Shalini 😊",
    author: "Chidambaram",
    date: "April 2025",
    rating: 5
  },
  {
    text: "Staying at <PERSON><PERSON><PERSON> ma'am's family home was not just a stay—it was an immersion into art, heritage, and heartfelt hospitality. The house, lovingly designed by her legendary father, <PERSON><PERSON><PERSON><PERSON>, is a living tribute to his legacy.",
    author: "<PERSON><PERSON>",
    date: "July 2025",
    rating: 5
  },
  {
    text: "My stay was nothing short of magical. This stay has truly become my cherished home away from home, & I can't wait to return soon.",
    author: "Srikanth",
    date: "March 2024",
    rating: 5
  },
  {
    text: "We travel a lot, mostly for work but Shalini's place stand apart in every way. From her warm and friendly attitude to a tastefully furnished room with a large balcony right above her garden.",
    author: "Nandakumar",
    date: "3 weeks ago",
    rating: 5
  },
  {
    text: "Yes! indeed it was great experience. I am so happy that I found a good place to stay in Aluva. I personally recommend anyone who wish to have peaceful stay.",
    author: "Ranjan",
    date: "March 2025",
    rating: 5
  },
  {
    text: "Felt like home, very friendly, calm and quiet place to stay with family. Beautiful house and well maintained surroundings, flexible check in and check out. Just loved it.",
    author: "Soja",
    date: "February 2025",
    rating: 5
  },
  {
    text: "This place vibes exactly like in the pictures, one of the safest decisions I made by choosing to stay in here whilst my time in Kochi, so much so that when in a touristic situation, I found myself saying, 'I just wanna go home'.",
    author: "Lekha",
    date: "October 2024",
    rating: 5
  },
  {
    text: "I had a comfortable stay and felt like home. The host was incredibly polite and treated us like family. The atmosphere was very homely, comfortable, and cozy. A truly delightful experience!",
    author: "DrShilpa",
    date: "January 2025",
    rating: 4
  },
  {
    text: "Best stay ever, the host made it all very comfortable, she is really a super host! The place looks way too artistic and serene, more like a heritage property! Highly recommend this place especially to solo woman travellers.",
    author: "Nivetha",
    date: "April 2024",
    rating: 5
  },
  {
    text: "Felt homely. I had a wonderful stay at Miss Shalini's Airbnb. The space was cozy, clean, and beautifully decorated, and she was incredibly welcoming and helpful. Highly recommend this lovely home away from home! Very picturesque house and the surroundings.",
    author: "Sunil",
    date: "August 2024",
    rating: 5
  },
  {
    text: "Neat, clean and esthetically designed room.",
    author: "Ramawtar",
    date: "June 2025",
    rating: 5
  },
  {
    text: "i love the place and bedroom wall inscription was good.",
    author: "Shiju",
    date: "April 2025",
    rating: 5
  },
  {
    text: "wonderful place to be and hosts are very cool and composed",
    author: "Kumar",
    date: "April 2025",
    rating: 5
  }
];

export async function GET() {
  try {
    // Add a small delay to simulate network request and ensure main page loads first
    await new Promise(resolve => setTimeout(resolve, 100));
    
    return NextResponse.json({
      reviews: ALL_REVIEWS,
      total: ALL_REVIEWS.length,
      success: true
    });
  } catch (error) {
    console.error('Error fetching reviews:', error);
    return NextResponse.json(
      { error: 'Failed to fetch reviews', success: false },
      { status: 500 }
    );
  }
}
