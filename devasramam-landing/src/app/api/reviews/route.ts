import { NextResponse } from 'next/server';
import { ALL_TESTIMONIALS } from '@/lib/all-testimonials';

// Use all testimonials from the existing file
const ALL_REVIEWS = ALL_TESTIMONIALS;

export async function GET() {
  try {
    // Add a small delay to simulate network request and ensure main page loads first
    await new Promise(resolve => setTimeout(resolve, 100));
    
    return NextResponse.json({
      reviews: ALL_REVIEWS,
      total: ALL_REVIEWS.length,
      success: true
    });
  } catch (error) {
    console.error('Error fetching reviews:', error);
    return NextResponse.json(
      { error: 'Failed to fetch reviews', success: false },
      { status: 500 }
    );
  }
}
